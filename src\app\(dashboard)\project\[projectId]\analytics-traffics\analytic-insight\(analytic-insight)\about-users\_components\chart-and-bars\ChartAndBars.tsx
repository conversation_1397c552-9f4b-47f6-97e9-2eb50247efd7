"use client";
import React, { useEffect, useRef, useState } from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "../../../../../_components/date-range/DateRange";
import CardTab from "@/components/ui/card-tab/CardTab";
import GSCLineChart from "../../../../_components/line-chart/LineChart";
import ProgressBar from "../../../../../overview/(overview)/users-overview/_components/ProgressBar";
import useChartAndBars from "./ChartAndBars.hooks";
import NoData from "../../../../_components/NoData";
import Dropdown from "@/components/ui/Dropdown";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* ================================= LODASH ================================= */
import isEqual from "lodash.isequal";

/* ================================== TYPES ================================= */
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import LineChartSkeleton from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/line-chart-skeleton/LineChartSkeleton";

/* ========================================================================== */
const ChartAndBars = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [activeTab, setActiveTab] = useState("");
  const { themeColor } = useAppThemeColor();
  const filters = ["Age", "Countries", "Cities", "Language", "Gender"];
  const [activeTabFilter, setActiveTabFilter] = useState(filters[0]);

  const domesticInfoFilters = ["Demographic info ", "Tech info "];
  const [activeDomesticFilter, setActiveDomesticFilter] = useState(
    domesticInfoFilters[0]
  );

  const { data, isError, error, isLoading } = useChartAndBars({
    tab: activeTab,
    domesticFilter: activeDomesticFilter,
    tabFilter: activeTabFilter,
  });

  const prevCardTabsRef = useRef<CardTabType[] | null>(data?.cardTabs ?? null);
  const [cardsDataChanged, setCardsDataChanged] = useState(false);

  useEffect(() => {
    if (data?.cardTabs && !isEqual(prevCardTabsRef.current, data.cardTabs)) {
      prevCardTabsRef.current = data.cardTabs;
      setCardsDataChanged(true);
    } else {
      setCardsDataChanged(false);
    }
  }, [data?.cardTabs]);

  useEffect(() => {
    if (data && !activeTab) setActiveTab(data?.cardTabs[0].title);
  }, [data, activeTab]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  if (!isError)
    return (
      <Card className="space-y-4">
        <div className="space-y-2">
          <div className="flex w-full justify-between items-center">
            <Title>About Users</Title>
            <Dropdown>
              <Dropdown.Button className="">
                {activeDomesticFilter}
              </Dropdown.Button>
              <Dropdown.Options>
                {domesticInfoFilters.map((filter, index) => (
                  <Dropdown.Option
                    key={index}
                    onClick={() => setActiveDomesticFilter(filter)}
                  >
                    {filter}
                  </Dropdown.Option>
                ))}
              </Dropdown.Options>
            </Dropdown>
          </div>
          <DateRange />
        </div>
        <div className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap">
          {cardsDataChanged ? (
            <motion.div
              className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
              key={"loading"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
              {Array.from({ length: 7 }).map((_, i) => (
                <CardTab key={i} isLoading />
              ))}
            </motion.div>
          ) : (
            prevCardTabsRef.current && (
              <motion.div
                className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
                key={"data"}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                {prevCardTabsRef.current.map(
                  (
                    {
                      title,
                      changeValue,
                      value,
                    }: { title: string; changeValue: string; value: string },
                    index: number
                  ) => (
                    <CardTab
                      key={index}
                      title={title}
                      value={value}
                      changeValue={changeValue}
                      className={`border-2 ${
                        activeTab === title
                          ? "border-primary"
                          : "border-transparent"
                      }`}
                      style={
                        activeTab === title
                          ? { borderColor: themeColor }
                          : { borderColor: "transparent" }
                      }
                      onSelect={() => setActiveTab(title)}
                    />
                  )
                )}
              </motion.div>
            )
          )}
        </div>
        <div className="w-full flex justify-end">
          <Dropdown>
            <Dropdown.Button className="min-w-24 text-sm">
              {activeTabFilter}
            </Dropdown.Button>
            <Dropdown.Options>
              {filters.map((filter, index) => (
                <Dropdown.Option
                  key={index}
                  onClick={() => {
                    setActiveTabFilter(filter);
                  }}
                >
                  {filter}
                </Dropdown.Option>
              ))}
            </Dropdown.Options>
          </Dropdown>
        </div>
        <div className="flex flex-col-reverse lg:flex-row items-center lg:items-start gap-y-16 min-h-[310px]">
          <div className=" w-full">
            {isLoading ? (
              <motion.div
                key={"loading"}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <LineChartSkeleton />
              </motion.div>
            ) : (
              data && (
                <motion.div
                  key={"data"}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <GSCLineChart
                    lineChartData={data.lineChartData}
                    colors={data.colors}
                    selectedLines={data.selectedLines}
                    cardsData={data.cardsData}
                  />
                </motion.div>
              )
            )}
          </div>

          <div className="w-full lg:w-[40%] space-y-2">
            {data
              ? data.progressbarData.map(({ title, percentage }, index) => (
                  <ProgressBar
                    key={index}
                    isLoading={false}
                    percentage={percentage}
                    title={title}
                    color={index === 0 ? "bg-[#F8BD00]" : ""}
                  />
                ))
              : !data &&
                Array.from({ length: 7 }).map((_, index) => (
                  <ProgressBar
                    key={index}
                    isLoading={true}
                    percentage={Math.floor(Math.random() * 101)}
                    title={"loading..."}
                    color={index === 0 ? "bg-[#F8BD00]" : ""}
                  />
                ))}
          </div>
        </div>
      </Card>
    );

  if (isError || !data) {
    isError && console.error("fetching chart and bars failed: ", error);
    return <NoData title="About Users" />;
  }
};

export default ChartAndBars;
