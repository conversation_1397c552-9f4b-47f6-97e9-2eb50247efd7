import AXIOS from "@/lib/axios";
import { useQuery } from "@tanstack/react-query";
import type {
  UsersOverviewData,
  CountriesAPIResponse,
} from "./UsersOverview.type";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import {
  transformCountriesData,
  transformToProgressBarData,
  isValidCountriesResponse,
  generateMockProgressBarData,
} from "./utils/dataTransform";

/**
 * Custom hook for fetching user overview data from GA4 API
 * @param userOverviewQuery - The selected tab (Countries, Cities, Gender, Device, Age)
 * @returns Query result with map data and progress bar data
 */
const useUsersOverview = (userOverviewQuery: string) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates } = useDateRangeStore();

  // Get formatted dates for API call (YYYY-MM-DD format)
  const { startDate, endDate } = getFormattedDates();

  return useQuery({
    queryKey: [
      "user-overview-data",
      projectId,
      userOverviewQuery,
      startDate,
      endDate,
    ],
    queryFn: async (): Promise<UsersOverviewData> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // ALL TABS USE THE SAME MAP DATA (countries data from GA4 API)
      // This ensures consistent geographic visualization across all tabs
      // Only the progress bar data changes based on the selected tab

      // Build API URL with new GA4 endpoint format
      const apiUrl = `/api/project/GA4/user/demographic/countries/${projectId}/`;

      // Build query parameters
      const params: Record<string, string> = {};

      // Add date parameters if available (YYYY-MM-DD format)
      // If no dates selected, API defaults to last 30 days
      if (startDate && endDate) {
        params.start_date = startDate;
        params.end_date = endDate;
      }

      const { data } = await AXIOS.get<CountriesAPIResponse>(apiUrl, {
        params: Object.keys(params).length > 0 ? params : undefined,
      });

      // Validate the response
      if (!isValidCountriesResponse(data)) {
        throw new Error("Invalid countries API response format");
      }

      // Transform the countries data for the maps - SAME FOR ALL TABS
      const mapData = transformCountriesData(data);

      // Generate progress bar data based on selected tab
      let progressbarData: any[];

      if (userOverviewQuery.toLowerCase() === "countries") {
        // Use actual countries data for progress bar
        progressbarData = transformToProgressBarData(data);
      } else {
        // Use mock data for other tabs (Cities, Gender, Device, Age)
        // This ensures all tabs use the same age-style data structure
        progressbarData = generateMockProgressBarData(userOverviewQuery);
      }

      return {
        // Maps always use the same countries data regardless of selected tab
        leftMap: mapData.leftMap,
        rightMap: mapData.rightMap,
        // Progress bar data varies by tab
        progressbarData: progressbarData,
      };
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export default useUsersOverview;
