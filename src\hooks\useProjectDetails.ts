import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { projectAPI, ProjectResponse } from "@/services/projectService";
import { useProjectThemeColor } from "@/store/useProjectThemeColor";

interface UseProjectDetailsOptions {
  projectId: string | null;
  enabled?: boolean;
}

export const useProjectDetails = ({ 
  projectId, 
  enabled = true 
}: UseProjectDetailsOptions) => {
  const { setCurrentProject, currentProject } = useProjectThemeColor();

  // Fetch project details
  const {
    data: projectData,
    isLoading,
    error,
    isSuccess,
    refetch,
  } = useQuery({
    queryKey: ["project-details", projectId],
    queryFn: () => projectAPI.getProject(projectId!),
    enabled: enabled && !!projectId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });

  // Update the store when project data is fetched
  useEffect(() => {
    if (isSuccess && projectData?.data) {
      setCurrentProject(projectData.data);
    }
  }, [isSuccess, projectData, setCurrentProject]);

  // Clear current project when projectId changes to null
  useEffect(() => {
    if (!projectId) {
      setCurrentProject(null);
    }
  }, [projectId, setCurrentProject]);

  return {
    project: projectData?.data || null,
    isLoading,
    error,
    isSuccess,
    refetch,
    // Also return the current project from store for immediate access
    currentProject,
  };
};
