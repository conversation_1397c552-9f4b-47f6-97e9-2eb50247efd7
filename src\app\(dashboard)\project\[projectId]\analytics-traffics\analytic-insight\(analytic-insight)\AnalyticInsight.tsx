"use client";
import { useSearchParams } from "next/navigation";
import { useEffect, useRef, useState, useCallback, useMemo } from "react";

/* ================================== TYPES ================================= */
import type { ActiveTab } from "./audience/Audience.types";

/* =============================== HEADLESS UI ============================== */
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from "@headlessui/react";

/* =============================== FRAMER MOTION ============================ */
import { motion } from "framer-motion";

/* =============================== STORE =============================== */
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";

/* =============================== COMPONENTS =============================== */
import SharedAnalyticsHeader from "../../_components/SharedAnalyticsHeader";
import Audience from "./audience/Audience";
import AboutUsers from "./about-users/AboutUsers";
import Traffics from "./traffics/Traffics";
import PagesInsight from "./pages-insight/PagesInsight";
import EventsAndConversions from "./events-and-conversions/EventsAndConversions";

/* ========================================================================== */
const AnalyticInsight = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */

  const [selectedIndex, setSelectedIndex] = useState(0);
  const tabRefs = useRef<HTMLElement[]>([]);
  const [activeTab, setActiveTab] = useState<ActiveTab | null>(null);
  const { themeColor } = useAppThemeColor();
  const searchParams = useSearchParams();
  const tabParam = searchParams?.get("tab");

  const tabs = useMemo(
    () => [
      { tab: "Audience", content: <Audience /> },
      { tab: "About Users", content: <AboutUsers /> },
      { tab: "Traffics", content: <Traffics /> },
      { tab: "Pages", content: <PagesInsight /> },
      { tab: "Events & Conversions", content: <EventsAndConversions /> },
    ],
    []
  );

  const tabNameToIndex = useMemo(
    () =>
      tabs.reduce((acc, { tab }, idx) => {
        acc[tab.toLowerCase().replace(/\s+/g, "-")] = idx;
        return acc;
      }, {} as Record<string, number>),
    [tabs]
  );

  /* ========================================================================== */
  /*                                  HANDLERS                                 */
  /* ========================================================================== */

  const handleIndicatorPosition = useCallback((index: number) => {
    const currentTab = tabRefs.current[index];
    if (currentTab) {
      setActiveTab({
        height: currentTab.offsetHeight,
        left: currentTab.offsetLeft,
        width: currentTab.offsetWidth,
        top: currentTab.offsetTop,
      });
    }
  }, []);

  const handleTabChange = useCallback(
    (index: number) => {
      setSelectedIndex(index);
      handleIndicatorPosition(index);
    },
    [handleIndicatorPosition]
  );

  /* ========================================================================== */
  /*                                  EFFECTS                                   */
  /* ========================================================================== */

  useEffect(() => {
    handleTabChange(0);
  }, [handleTabChange]);

  useEffect(() => {
    if (tabParam) {
      const normalized = tabParam.toLowerCase().replace(/\s+/g, "-");
      const index = tabNameToIndex[normalized];
      if (typeof index === "number" && index !== selectedIndex) {
        handleTabChange(index);
      }
    }
  }, [tabParam, selectedIndex, tabNameToIndex, handleTabChange]);

  /* ========================================================================== */
  /*                                  RENDER                                   */
  /* ========================================================================== */

  return (
    <div className="w-full space-y-4">
      <SharedAnalyticsHeader title="Analytic Insight" />

      <TabGroup
        selectedIndex={selectedIndex}
        onChange={handleTabChange}
        className="space-y-4"
      >
        <TabList className="space-x-2 relative overflow-x-auto flex items-center flex-nowrap">
          {tabs.map(({ tab }, index) => (
            <Tab
              key={tab}
              onClick={() => handleIndicatorPosition(index)}
              ref={(el) => {
                if (el) tabRefs.current[index] = el;
              }}
              className="px-4 py-2 aria-selected:text-primary text-nowrap"
              style={{
                color:
                  selectedIndex === index
                    ? themeColor
                    : "var(--color-secondary)",
              }}
            >
              {tab}
            </Tab>
          ))}
          {activeTab && (
            <motion.div
              layout
              initial={false}
              animate={activeTab}
              transition={{ type: "tween", ease: "easeInOut" }}
              className="h-8 w-8 bg-primary/10 rounded-lg absolute top-0"
              style={{
                backgroundColor: themeColor + "10",
                height: activeTab.height,
                width: activeTab.width,
                left: activeTab.left,
                top: activeTab.top,
              }}
            />
          )}
        </TabList>

        <TabPanels>
          {tabs.map(({ content }, index) => (
            <TabPanel
              hidden={index !== selectedIndex}
              key={index}
              as={motion.div}
              initial={{ x: 100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -100, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              {content}
            </TabPanel>
          ))}
        </TabPanels>
      </TabGroup>
    </div>
  );
};

export default AnalyticInsight;
