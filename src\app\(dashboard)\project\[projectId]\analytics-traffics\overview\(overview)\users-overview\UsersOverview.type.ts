import { ProgressbarData } from "../../../types/AnalyticsTraffics.types";

// GA4 API Response Types for User Countries Data
export interface CountryItem {
  country: string; // Country name (e.g., "United States")
  users: number; // Number of users from this country
  percentage: number; // Percentage of total users
}

export interface CountryDataTotals {
  total_users: number; // Total users across all countries
}

export interface CountryDataPeriod {
  start_date: string; // Start date in YYYY-MM-DD format
  end_date: string; // End date in YYYY-MM-DD format
}

export interface CountryData {
  countries: CountryItem[]; // Array of country data
  totals: CountryDataTotals; // Total metrics
  period: CountryDataPeriod; // Date range for the data
}

export interface CountriesAPIResponse {
  success: boolean; // Success status
  project_id: string; // Project identifier
  metric: string; // Metric type (e.g., "user_countries")
  data: CountryData; // Country data
  last_updated: string; // Last update timestamp
}

// Transformed Data Types for Components
export interface CountryMapData {
  leftMap: Record<string, string>;
  rightMap: Record<string, string>;
}

// Legacy type for backward compatibility
export type UsersOverviewData = {
  leftMap: Record<string, string>;
  rightMap: Record<string, string>;
  progressbarData: ProgressbarData[];
};
