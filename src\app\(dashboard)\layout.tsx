"use client";
import "@/app/globals.css";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";

/* ================================= LEAFLET ================================ */
import "leaflet/dist/leaflet.css";

/* =============================== REACT QUERY ============================== */
import nunitoSansFont from "@/constants/localFont";
import {
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";

/* ================================ SERVICES ================================ */
import { logout } from "@/services/authService";

/* ================================== AXIOS ================================= */
import axios from "axios";
import { useAuthStore } from "@/store/authStore";

/* =============================== COMPONENTS =============================== */
import NavbarDashboard from "@/components/dashboard/NavbarDashboard";
import LoadingQuery from "@/components/shared/LoadingQuery";
import DashboardSidebar from "@/components/layout/DashboardSidebar/DashboardSidebar";
import Breadcrumb from "@/components/ui/Breadcrumb";
import { AuthProvider } from "@/providers/AuthProvider";
import DashboardContent from "@/app/(dashboard)/DashboardContent";
import { showToast } from "@/lib/toast";
import { ToastProvider } from "@/lib/ToastProvider";
import "react-day-picker/style.css";
import { fastRedirectIfNoAuth } from "@/utils/fastAuthCheck";
axios.defaults.baseURL =
  process.env.NEXT_PUBLIC_API_URL || "https://seoanalyser.com.au";
axios.head;

/* ========================================================================== */
// Protected Dashboard Layout Wrapper
function ProtectedDashboardWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();

  // Immediate token check and redirect on layout mount
  useEffect(() => {
    const redirected = fastRedirectIfNoAuth(router, pathname || "");
    if (redirected) {
      console.log("Dashboard layout: Redirected user without tokens");
    }
  }, [router, pathname]);

  return <DashboardContent>{children}</DashboardContent>;
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const logout = useAuthStore((state) => state.logout);
  const pathname = usePathname();
  const route = useRouter();
  const { isAuthenticated, fetchProfile } = useAuthStore();

  // Check if current route is my-projects to adjust layout
  const isMyProjectsRoute = pathname === "/my-projects";

  // Check authentication on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const isValid = await fetchProfile(true);
        if (!isValid) {
          route.push("/login");
        }
      } catch (error) {
        console.error("Dashboard auth check failed:", error);
        route.push("/login");
      }
    };

    checkAuth();
  }, [fetchProfile, route]);

  const query = new QueryClient({
    queryCache: new QueryCache({
      onError: (err: any) => {
        if (
          err?.response?.data?.detail ===
            "Given token not valid for any token type" ||
          err?.response?.status === 401
        ) {
          showToast.error("Session expired. Please log in again!");
          // Use optimistic logout and redirect immediately
          logout();
          // Force redirect to home page
          if (typeof window !== "undefined") {
            window.location.replace("/");
          }
        } else {
          showToast.error("A database connection error occurred");
        }
      },
    }),
  });

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  return (
    <html lang="en" className="h-full">
      <body
        className={`${nunitoSansFont.variable} font-[family-name:var(--font-nunito-sans)] antialiased relative h-full dashboard-page`}
      >
        <QueryClientProvider client={query}>
          <AuthProvider>
            <ProtectedDashboardWrapper>
              <Breadcrumb />
              <div
                className={`flex gap-4 ${
                  isMyProjectsRoute ? "h-full overflow-hidden" : "pb-6"
                }`}
              >
                <DashboardSidebar />
                {children}
              </div>
            </ProtectedDashboardWrapper>
            <ToastProvider />
          </AuthProvider>
        </QueryClientProvider>
      </body>
    </html>
  );
}
