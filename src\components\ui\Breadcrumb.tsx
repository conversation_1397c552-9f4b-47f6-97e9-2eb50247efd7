"use client";
import React from "react";

/* =============================== COMPONENTS =============================== */
import { usePathname, useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import Dropdown from "@/components/ui/Dropdown";

/* ================================= SERVICES ================================ */
import projectAPI, { ProjectListItem } from "@/services/projectService";

/* ================================= HOOKS ================================== */
import { useProjectId } from "@/hooks/useProjectId";
import { useProjectThemeColor } from "@/store/useProjectThemeColor";

/* ========================================================================== */
const Breadcrumb = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const { setCurrentProject } = useProjectThemeColor();
  const pathname = usePathname();
  const router = useRouter();
  const { projectId } = useProjectId();

  const pathSegments = pathname
    ?.split("/")
    .filter(Boolean)
    .filter((segment) => {
      // Filter out project ID (UUID pattern)
      const uuidPattern =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      return !uuidPattern.test(segment);
    })
    .map((segment) =>
      segment
        .split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
    );

  const displayInRouts = pathname?.startsWith("/project");

  // Fetch projects from API
  const {
    data: projectsResponse,
    isLoading: projectsLoading,
    isError: projectsError,
  } = useQuery({
    queryKey: ["AllProjects"],
    queryFn: async () => {
      const response = await projectAPI.getAllProjects({
        ordering: "-created_at",
        page_size: 50,
      });
      return response.data;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
    enabled: displayInRouts, // Only fetch when on project routes
  });

  const projects = projectsResponse?.results || [];

  // Find current project based on projectId
  const currentProject = projects.find((project) => project.id === projectId);

  // Handle project selection
  const handleProjectSelect = (project: ProjectListItem) => {
    // Create a project response object to match the expected format
    const projectResponse = {
      id: project.id,
      project_name: project.project_name,
      url: project.url,
      project_color: project.project_color,
      status: project.status,
      created_at: project.created_at,
      updated_at: project.created_at, // Use created_at as fallback
      domain_type: "", // Not available in list item
    };

    // Update current project and theme color
    setCurrentProject(projectResponse);

    // Navigate to the selected project
    router.push(`/project/${project.id}`);
  };

  // Helper function to get domain from URL
  const getDomain = (url: string) => {
    try {
      return new URL(url).hostname.replace("www.", "");
    } catch {
      return url;
    }
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  if (!displayInRouts) return;

  return (
    <div className="flex flex-col-reverse md:flex-row justify-between mb-3.5">
      <div className="flex items-center gap-2 py-2.5 text-xs text-gray-400">
        {pathSegments?.map((crumb, index) => (
          <div key={index} className="flex gap-2">
            <span className={`cursor-default last:text-secondary`}>
              {crumb}
            </span>
            {index === pathSegments.length - 1 ? null : (
              <span className="cursor-default">|</span>
            )}
          </div>
        ))}
      </div>

      <div className="flex justify-end">
        <Dropdown>
          <Dropdown.Button
            className="bg-white py-2 h-10"
            style={{
              backgroundColor: currentProject
                ? `${currentProject.project_color}1A`
                : "#fff",
              border: `1px solid ${
                currentProject ? currentProject.project_color : "transparent"
              }`,
              width: "200px",
              minWidth: "200px",
            }}
          >
            <span
              style={{
                color: currentProject
                  ? currentProject.project_color
                  : "#344054",
              }}
            >
              {projectsLoading
                ? "Loading projects..."
                : projectsError
                ? "Error loading projects"
                : currentProject
                ? `${currentProject.project_name} (${getDomain(
                    currentProject.url
                  )})`
                : "Select your project"}
            </span>
          </Dropdown.Button>
          <Dropdown.Options className="bg-white shadow-md max-h-64 overflow-y-auto !w-[200px] !min-w-[200px]">
            {projects.length > 0 ? (
              projects.map((project) => (
                <Dropdown.Option
                  key={project.id}
                  className="flex items-center justify-between transition-colors duration-200 relative"
                  onClick={() => handleProjectSelect(project)}
                >
                  {/* Background with project color */}
                  <div
                    className="absolute inset-0 opacity-10"
                    style={{
                      backgroundColor: project.project_color,
                    }}
                  />
                  <div className="flex flex-col min-w-0 flex-1 relative z-10">
                    <span
                      className="font-medium truncate"
                      style={{
                        color: project.project_color,
                      }}
                    >
                      {project.project_name}
                    </span>
                    <span
                      className="text-xs truncate opacity-70"
                      style={{
                        color: project.project_color,
                      }}
                    >
                      {getDomain(project.url)}
                    </span>
                  </div>
                  {project.id === projectId && (
                    <div className="w-2 h-2 rounded-full bg-primary flex-shrink-0 relative z-10" />
                  )}
                </Dropdown.Option>
              ))
            ) : (
              <Dropdown.Option className="text-gray-500 text-center py-4">
                {projectsLoading ? "Loading..." : "No projects found"}
              </Dropdown.Option>
            )}
          </Dropdown.Options>
        </Dropdown>
      </div>
    </div>
  );
};

export default Breadcrumb;
