import { useEffect, useId, useRef } from "react";
import ReactDOM from "react-dom/client";

/* =================================== D3 =================================== */
import * as d3 from "d3";
import type {
  Feature,
  GeoJsonProperties,
  Geometry,
  FeatureCollection,
} from "geojson";

/* ================================ CONSTANTS =============================== */
import { countryNames } from "@/constants/countryMapping";
import { cn } from "@/utils/cn";

/* ================================== TYPES ================================= */
type TooltipContentProps = Record<
  "name" | "value" | "users" | "percent",
  string
>;
type CountryValues = Record<string, string>;

interface ChoroplethMapProps {
  color: string;
  countryValues: CountryValues;
  className?: string;
}

/* ========================================================================== */
/*                                 COMPONENTS                                 */
/* ========================================================================== */
const TooltipContent = ({
  name,
  value,
  users,
  percent,
}: TooltipContentProps) => (
  <div className="text-secondary flex flex-col gap-1">
    <div className="text-xs space-x-1 font-bold">
      <span>{name}</span>
      {percent && <span className="text-primary-green">{percent}%</span>}
    </div>
    <span>{value || 0}%</span>
    <div className="flex text-nowrap gap-1 text-secondary/50 font-bold">
      <span>{users || 0}</span>
      <span>users</span>
    </div>
  </div>
);

/* ========================================================================== */
const ChoroplethMap = ({
  color,
  countryValues,
  className,
}: ChoroplethMapProps) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const tooltipId = useId();
  const svgRef = useRef<SVGSVGElement | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  /* ========================================================================== */
  /*                                 USE EFFECT                                 */
  /* ========================================================================== */
  useEffect(() => {
    function drawMap() {
      if (!svgRef.current || !containerRef.current) return;

      const { width, height } = containerRef.current.getBoundingClientRect();

      const svg = d3.select(svgRef.current);
      svg.attr("width", width).attr("height", height);
      svg.selectAll("*").remove();

      d3.json("/data/world.geo.json").then((data) => {
        const geoData = data as FeatureCollection<Geometry, GeoJsonProperties>;
        const projection = d3
          .geoMercator()
          .scale(width / 9)
          .translate([width / 2, height / 2]);

        const pathGenerator = d3.geoPath().projection(projection);

        const colorScale = d3
          .scaleSequential()
          .domain([0, 100])
          .interpolator(d3.interpolateRgb("#A0ACC180", color));
        svg
          .selectAll<SVGPathElement, Feature<Geometry, GeoJsonProperties>>(
            ".country"
          )
          .data(geoData.features)
          .join("path")
          .join("path")
          .attr("class", "country")
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          .attr("d", pathGenerator as any)
          .attr("fill", (d) => {
            const code = d.properties?.adm0_a3_br;
            const raw = code ? countryValues[code] : undefined;

            if (!raw) return "#A0ACC180";

            const score = raw.split(",")[0];
            const scoreNum = Number(score);

            return isNaN(scoreNum) ? "#A0ACC180" : colorScale(scoreNum);
          })
          .attr("stroke", "#fff")
          .attr("strokeWidth", 0.5)
          .on(
            "mouseover",
            function (
              event: MouseEvent,
              d: Feature<Geometry, GeoJsonProperties>
            ) {
              const code = d.properties?.adm0_a3_br;
              const name = code ? countryNames[code] ?? code : "Unknown";
              const raw =
                code && countryValues[code] ? countryValues[code] : "";
              const [score = "", users = "", percent = ""] = raw
                .split(",")
                .map((s) => s.trim());

              const tooltipNode = document.getElementById(tooltipId);
              if (tooltipNode) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                if (!(tooltipNode as any)._root) {
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  (tooltipNode as any)._root = ReactDOM.createRoot(tooltipNode);
                }
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (tooltipNode as any)._root.render(
                  <TooltipContent
                    name={name}
                    value={score}
                    users={users}
                    percent={percent}
                  />
                );

                d3.select(tooltipNode).style("opacity", "1");
              }
            }
          )
          .on("mousemove", function (event) {
            const [x, y] = d3.pointer(event, containerRef.current);

            d3.select(`#${tooltipId}`)
              .style("left", `${x + 10}px`)
              .style("top", `${y - 20}px`);
          })
          .on("mouseout", function () {
            d3.select(`#${tooltipId}`).style("opacity", "0");
          });
      });
    }

    drawMap();

    window.addEventListener("resize", drawMap);
    return () => window.removeEventListener("resize", drawMap);
  }, [color, countryValues, tooltipId]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div
      ref={containerRef}
      className={cn(
        " relative w-full lg:w-[260px] xl:w-[450px] h-[250px] md:h-[400px] lg:h-[300px] mx-auto",
        className
      )}
    >
      <svg ref={svgRef} className="h-[78%] md:h-[100%] lg:h-[78%]"></svg>
      <div className="w-full h-6 px-4 space-y-2">
        <div
          className="h-3 rounded-full w-full"
          style={{
            background: `linear-gradient(to left, ${color}, white)`,
            border: `2px solid ${color}`,
          }}
        />
        <div className="flex text-[10px] text-secondary justify-between">
          <span>0</span>
          <span>3000</span>
        </div>
      </div>
      <div
        id={tooltipId}
        className="absolute pointer-events-none bg-white shadow-2xl text-secondary text-xs rounded px-2 py-1 opacity-0 transition-opacity duration-200 z-50"
      />
    </div>
  );
};

export default ChoroplethMap;
