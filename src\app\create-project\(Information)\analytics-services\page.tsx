"use client";
import BoxCreateProject from "@/components/CreateProject/BoxCreateProject";
import InfoCard from "@/components/CreateProject/InfoCard";
import NavbarCreateProject from "@/components/CreateProject/NavbarCreateProject";
import StepMobileCreateProject from "@/components/CreateProject/StepMobileCreateProject";
import TitleCreateProject from "@/components/CreateProject/TitleCreateProject";
import ButtenSubmit from "@/components/shared/ButtenSubmit";
import { useMutation, useQuery } from "@tanstack/react-query";
import Image from "next/image";
import Link from "next/link";
import React, { useState, useEffect } from "react";
import { useCreateProjectStore } from "@/store/createProjectStore";
import createProjectToast from "@/lib/createProjectToast";
import PageTransition, {
  AnimatedElement,
} from "@/components/CreateProject/PageTransition";
import {
  projectAPI,
  UpdateProjectRequest,
  ProjectSearchEngine,
  ProjectKeyword,
  ProjectCompetitor,
  GoogleAnalyticsConnectRequest,
  GoogleAnalyticsProperty,
  GoogleSearchConsoleConnectRequest,
  GoogleSearchConsoleProperty,
} from "@/services/projectService";
import { useRouter, useSearchParams } from "next/navigation";
import { useEditNavigation } from "@/hooks/useEditProject";
import GoogleAnalyticsPropertyDialog from "@/components/CreateProject/GoogleAnalyticsPropertyDialog";
import GoogleSearchConsolePropertyDialog from "@/components/CreateProject/GoogleSearchConsolePropertyDialog";
import { LogosGoogleSearchConsole } from "@/components/icons/LogosGoogleSearchConsole";

const buttons = [
  {
    name: "Connect Google Analytics",
    image: "/images/create-project/google-analytics.svg",
    id: 1,
  },
  {
    name: "Connect Google Search Console",
    image: "/images/create-project/google.svg",
    id: 2,
  },
];
export default function Page() {
  const [btnCheck, setBtnCheck] = useState<number | null>(null);
  const [showPropertyDialog, setShowPropertyDialog] = useState(false);
  const [showGSCPropertyDialog, setShowGSCPropertyDialog] = useState(false);
  const [gaStatusChecked, setGaStatusChecked] = useState(false);
  const [gscStatusChecked, setGscStatusChecked] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();

  // Debug modal state changes
  useEffect(() => {
    console.log("showPropertyDialog state changed:", showPropertyDialog);
  }, [showPropertyDialog]);

  // Testing flag to bypass status checking
  const TESTING_MODE = false;

  const {
    setCurrentStep,
    resetAll,
    projectInfo,
    searchEngineConfigs,
    keywords,
    setLoading,
  } = useCreateProjectStore();

  // Edit navigation hook for consistent URL building
  const { urls } = useEditNavigation();

  // Get project ID and method from query params
  const queryProjectId = searchParams?.get("pid");
  const method = searchParams?.get("m");

  // Determine the project ID to use - prioritize URL param (edit mode) then store (create mode)
  const projectId = queryProjectId || projectInfo?.id;

  // Check Google Analytics status when page loads - check if project ID exists from either source
  const { data: gaStatus } = useQuery({
    queryKey: ["google-analytics-status", projectId],
    queryFn: async () => {
      if (!projectId) return null;

      try {
        const response = await projectAPI.getGoogleAnalyticsStatus(projectId);
        return response.data;
      } catch (error: any) {
        // Handle 404 and other errors by returning the error response data
        if (error.response && error.response.data) {
          console.log("Google Analytics status error:", error.response.data);
          return error.response.data;
        }
        // For other errors, return a default error structure
        return {
          status: "error",
          message: "Failed to check Google Analytics connection status",
        };
      }
    },
    enabled: !!projectId,
    retry: false, // Don't retry on 404 errors
    retryDelay: 1000,
  });

  // Check Google Search Console status when page loads - check if project ID exists from either source
  const { data: gscStatus } = useQuery({
    queryKey: ["google-search-console-status", projectId],
    queryFn: async () => {
      if (!projectId) return null;

      try {
        const response = await projectAPI.getGoogleSearchConsoleStatus(
          projectId
        );
        return response.data;
      } catch (error: any) {
        // Handle 404 and other errors by returning the error response data
        if (error.response && error.response.data) {
          console.log(
            "Google Search Console status error:",
            error.response.data
          );
          return error.response.data;
        }
        // For other errors, return a default error structure
        return {
          status: "error",
          message: "Failed to check Google Search Console connection status",
        };
      }
    },
    enabled: !!projectId,
    retry: false, // Don't retry on 404 errors
    retryDelay: 1000,
  });

  // Helper function to check if Google Analytics is connected
  const isGoogleAnalyticsConnected = () => {
    // Only connected if connection exists and has success or connected status
    const isConnected =
      gaStatus?.connection &&
      (gaStatus.connection.status === "success" ||
        gaStatus.connection.status === "connected");

    console.log("isGoogleAnalyticsConnected check:", {
      gaStatus: gaStatus,
      hasConnection: !!gaStatus?.connection,
      connectionStatus: gaStatus?.connection?.status,
      isConnected: isConnected,
    });

    return isConnected;
  };

  // Helper function to check if Google Search Console is connected
  const isGoogleSearchConsoleConnected = () => {
    // Only connected if connection exists and has success or connected status
    return (
      gscStatus?.connection &&
      (gscStatus.connection.status === "success" ||
        gscStatus.connection.status === "connected")
    );
  };

  // Check Google Analytics status and show dialog if needed (only for G4a method or no method)
  useEffect(() => {
    console.log("Google Analytics useEffect triggered:", {
      projectId,
      gaStatus,
      gaStatusChecked,
      method,
      conditionMet:
        projectId && gaStatus && !gaStatusChecked && method === "GA4",
    });

    // Only run this logic if we're coming back from OAuth (method=GA4)
    // Don't run on normal page loads without method parameter
    if (projectId && gaStatus && !gaStatusChecked && method === "GA4") {
      setGaStatusChecked(true);

      console.log("Google Analytics status check:", {
        status: gaStatus.status,
        message: gaStatus.message,
        connection: gaStatus.connection,
        method: method,
      });

      // Don't show modal if status is error (including 404 no connection found)
      const isErrorStatus = gaStatus.status === "error";

      // Specifically check for "no connection found" message
      const isNoConnectionError =
        isErrorStatus &&
        gaStatus.message &&
        gaStatus.message
          .toLowerCase()
          .includes("no google analytics connection found");

      // Only show modal if:
      // 1. NOT an error status
      // 2. Status is success/pending OR connection exists with success/pending status
      const shouldShowModal =
        !isErrorStatus &&
        (gaStatus.status === "pending" ||
          gaStatus.status === "success" ||
          (gaStatus.connection &&
            (gaStatus.connection.status === "pending" ||
              gaStatus.connection.status === "success")));

      console.log("Google Analytics modal decision:", {
        isErrorStatus,
        isNoConnectionError,
        shouldShowModal,
        gaStatusStatus: gaStatus.status,
        gaStatusMessage: gaStatus.message,
      });

      if (isErrorStatus) {
        console.log(
          "Google Analytics: Error status detected, skipping property dialog"
        );
      }

      if (isNoConnectionError) {
        console.log("Google Analytics: No connection found message detected");
      }

      if (shouldShowModal) {
        console.log("Google Analytics: Opening property dialog");
        setShowPropertyDialog(true);
      } else {
        console.log("Google Analytics: NOT opening property dialog");
      }
    }
  }, [gaStatus, gaStatusChecked, projectId, method]);

  // Check Google Search Console status and show dialog if needed (only for GSC method)
  useEffect(() => {
    if (projectId && gscStatus && !gscStatusChecked && method === "GSC") {
      setGscStatusChecked(true);

      // Don't show modal if status is error (including 404 no connection found)
      const isErrorStatus = gscStatus.status === "error";

      // Specifically check for "no connection found" message
      const isNoConnectionError =
        isErrorStatus &&
        gscStatus.message &&
        gscStatus.message
          .toLowerCase()
          .includes("no google search console connection found");

      // Only show modal if:
      // 1. NOT an error status
      // 2. Status is success/pending OR connection exists with success/pending status
      const shouldShowModal =
        !isErrorStatus &&
        (gscStatus.status === "pending" ||
          gscStatus.status === "success" ||
          (gscStatus.connection &&
            (gscStatus.connection.status === "pending" ||
              gscStatus.connection.status === "success")));

      if (isNoConnectionError) {
        console.log(
          "Google Search Console: No connection found, skipping property dialog"
        );
      }

      if (shouldShowModal) {
        setShowGSCPropertyDialog(true);
      }
    }
  }, [gscStatus, gscStatusChecked, projectId, method]);

  // Google Analytics connection mutation
  const { mutate: connectGA, isPending: gaConnecting } = useMutation({
    mutationFn: async (): Promise<any> => {
      if (!projectId) {
        throw new Error("Project ID is missing");
      }

      const data: GoogleAnalyticsConnectRequest = {
        project_id: projectId,
        redirect: "create",
      };

      // Use the centralized API call handler
      return await createProjectToast.apiCall(
        projectAPI.connectGoogleAnalytics(data),
        {
          loadingMessage: "Connecting to Google Analytics...",
          errorContext: "connect Google Analytics",
          onSuccess: (response) => {
            // Redirect to the authorization URL
            if (response.data.authorization_url) {
              window.location.href = response.data.authorization_url;
            }
            console.log(
              "Google Analytics authorization URL received:",
              response.data
            );
          },
        }
      );
    },
    onSuccess: () => {
      // Success handling is now done by createProjectToast.apiCall
      console.log("Google Analytics connection initiated successfully");
    },
    onError: (error: any) => {
      // Error handling is now done by createProjectToast.apiCall
      console.error("Error connecting Google Analytics:", error);
    },
  });

  // Google Search Console connection mutation
  const { mutate: connectGSC, isPending: gscConnecting } = useMutation({
    mutationFn: async (): Promise<any> => {
      if (!projectId) {
        throw new Error("Project ID is missing");
      }

      const data: GoogleSearchConsoleConnectRequest = {
        project_id: projectId,
        redirect: "create",
      };

      // Use the centralized API call handler
      return await createProjectToast.apiCall(
        projectAPI.connectGoogleSearchConsole(data),
        {
          loadingMessage: "Connecting to Google Search Console...",
          errorContext: "connect Google Search Console",
          onSuccess: (response) => {
            // Redirect to the authorization URL
            if (response.data.authorization_url) {
              window.location.href = response.data.authorization_url;
            }
            console.log(
              "Google Search Console authorization URL received:",
              response.data
            );
          },
        }
      );
    },
    onSuccess: () => {
      // Success handling is now done by createProjectToast.apiCall
      console.log("Google Search Console connection initiated successfully");
    },
    onError: (error: any) => {
      // Error handling is now done by createProjectToast.apiCall
      console.error("Error connecting Google Search Console:", error);
    },
  });

  // Project finalization mutation
  const { mutate, isPending } = useMutation({
    mutationFn: async (): Promise<any> => {
      if (!projectInfo || !projectId) {
        throw new Error("Project information or ID is missing");
      }

      setLoading(true);

      try {
        // Transform search engine configs to API format
        const primarySearchEngines: ProjectSearchEngine[] =
          searchEngineConfigs.map((config) => ({
            search_engine: config.searchEngine.name.toLowerCase(),
            countries: [config.country.code],
            languages: [config.language.code],
          }));

        // Transform keywords to API format
        const projectKeywords: ProjectKeyword[] = keywords.map((keyword) => {
          // Get the search engines and countries for this keyword based on its configIds
          const keywordConfigs = keyword.configIds
            .map((id) => searchEngineConfigs.find((config) => config.id === id))
            .filter(Boolean);

          const searchEngines = [
            ...new Set(
              keywordConfigs.map((config) =>
                config?.searchEngine.name.toLowerCase()
              )
            ),
          ].filter(Boolean) as string[];

          const countries = [
            ...new Set(keywordConfigs.map((config) => config?.country.code)),
          ].filter(Boolean) as string[];

          const languages = [
            ...new Set(keywordConfigs.map((config) => config?.language.code)),
          ].filter(Boolean) as string[];

          return {
            keyword: keyword.keyword,
            search_engines: searchEngines,
            countries: countries,
            languages: languages,
          };
        });

        // Transform competitors to API format
        const competitors: ProjectCompetitor[] = useCreateProjectStore
          .getState()
          .competitors.map((competitor) => ({
            url: competitor.domain.startsWith("http")
              ? competitor.domain
              : `https://${competitor.domain}`,
            search_engines: competitor.searchEngines,
            countries: competitor.countries,
          }));

        // Prepare the project update data with all required fields for PUT request
        const updateData: UpdateProjectRequest = {
          url: projectInfo.domain,
          domain_type: projectInfo.domain.startsWith("http")
            ? `*.${
                projectInfo.domain.replace(/^https?:\/\//, "").split("/")[0]
              }`
            : `*.${projectInfo.domain.split("/")[0]}`,
          project_name: projectInfo.name,
          project_color: projectInfo.color || "#8C00FF", // Default color if not set
          status: "enabled", // Required for UpdateProjectRequest
          primary_search_engines: primarySearchEngines,
          keywords: projectKeywords,
          competitors: competitors,
        };

        // Use the centralized API call handler for project finalization with PUT request
        return await createProjectToast.apiCall(
          projectAPI.updateProject(projectId, updateData),
          {
            loadingMessage: "Finalizing your project setup...",
            successMessage: "Project setup completed successfully!",
            errorContext: "finalize project",
          }
        );
      } finally {
        setLoading(false);
      }
    },
    onSuccess: () => {
      // Clear storage first
      resetAll();
      // Force a complete page navigation to bypass layout validation
      window.location.href = "/my-projects";
    },
    onError: (error: any) => {
      // Error handling is now done by createProjectToast.apiCall
      console.error("Error finalizing project:", error);
    },
  });

  // Handle property selection
  const handlePropertySelected = (property: GoogleAnalyticsProperty) => {
    console.log("Selected property:", property);
    setShowPropertyDialog(false);
    // You can add additional logic here to save the selected property
  };

  // Handle Google Search Console property selection
  const handleGSCPropertySelected = (property: GoogleSearchConsoleProperty) => {
    console.log("Selected GSC property:", property);
    setShowGSCPropertyDialog(false);
    // You can add additional logic here to save the selected property
  };

  useEffect(() => {
    setCurrentStep("analytics-services");

    // Note: Analytics services page doesn't need navigation validation
    // as it's the final step and should be accessible in both create and edit modes
  }, [setCurrentStep]);

  return (
    <PageTransition>
      <div className="flex justify-between flex-col gap-6 lg:gap-8 h-full min-h-[calc(100vh-2rem)]">
        <div className="flex flex-col gap-3">
          <AnimatedElement variant="child">
            <NavbarCreateProject>Analytics Services</NavbarCreateProject>
          </AnimatedElement>
          <AnimatedElement variant="child" delay={0.1}>
            <StepMobileCreateProject />
          </AnimatedElement>

          {/* Main Content Card */}
          <AnimatedElement variant="card" delay={0.2}>
            <BoxCreateProject classPlus="relative">
              {/* Header Section */}
              <AnimatedElement variant="child" delay={0.3}>
                <div className="mb-4">
                  <TitleCreateProject
                    head="Connect analytics services"
                    description="Integrate your Google Analytics and Search Console to unlock powerful insights and comprehensive SEO tracking. This connection enables advanced reporting and data synchronization."
                    classHead="text-lg lg:text-xl font-semibold text-[#344054]"
                    classP="text-[#344054] mt-3 leading-relaxed"
                  />
                </div>
              </AnimatedElement>

              {/* Service Integration Section */}
              <AnimatedElement variant="child" delay={0.4}>
                <div className="space-y-8">
                  {/* Service Selection Cards */}
                  <AnimatedElement variant="form" delay={0.5}>
                    <div className="bg-gray-50 rounded-xl p-6">
                      <div className="mb-6">
                        <h4 className="text-sm font-medium text-[#344054] mb-2">
                          Available Services
                        </h4>
                        <p className="text-xs text-[#344054]">
                          Select the analytics services you want to connect to
                          your project
                        </p>
                      </div>

                      <div className="grid gap-3 md:grid-cols-2">
                        {buttons.map((btn, index) => {
                          // Google Analytics button - standalone action button
                          if (btn.id === 1) {
                            const isConnected = isGoogleAnalyticsConnected();
                            return (
                              <button
                                onClick={() => {
                                  console.log(
                                    "Google Analytics button clicked:",
                                    {
                                      isConnected,
                                      TESTING_MODE,
                                      projectId,
                                      gaStatus,
                                    }
                                  );

                                  if (isConnected) {
                                    // If already connected, show property dialog
                                    console.log(
                                      "Opening property dialog because isConnected=true"
                                    );
                                    setShowPropertyDialog(true);
                                  } else if (TESTING_MODE) {
                                    // In testing mode, show modal directly
                                    console.log(
                                      "Opening property dialog because TESTING_MODE=true"
                                    );
                                    setShowPropertyDialog(true);
                                  } else if (projectId) {
                                    console.log("Initiating GA connection");
                                    connectGA();
                                  } else {
                                    console.log(
                                      "No action taken - missing projectId"
                                    );
                                  }
                                }}
                                disabled={gaConnecting}
                                key={index}
                                className={`group relative overflow-hidden transition-all duration-200 ease-in-out border rounded-lg p-4 flex items-center gap-3 text-left ${
                                  isConnected
                                    ? "border-green-200 bg-green-50 hover:bg-green-100"
                                    : "border-gray-200 bg-white hover:border-primary/50 hover:bg-primary/5"
                                } ${
                                  gaConnecting ? "opacity-70 cursor-wait" : ""
                                }`}
                              >
                                {/* Loading Indicator for Google Analytics */}
                                {gaConnecting && (
                                  <div className="absolute inset-0 bg-white/50 flex items-center justify-center">
                                    <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                                  </div>
                                )}
                                {/* Service Icon */}
                                <div className="flex-shrink-0">
                                  <Image
                                    alt={btn.name}
                                    src={btn.image}
                                    width={20}
                                    className="w-6 h-6"
                                    height={20}
                                  />
                                </div>
                                {/* Service Name */}
                                <div className="flex-1">
                                  <h5
                                    className={`text-sm font-medium transition-colors ${
                                      isConnected
                                        ? "text-green-700"
                                        : "text-[#344054] group-hover:text-primary"
                                    }`}
                                  >
                                    {gaConnecting
                                      ? "Connecting..."
                                      : isConnected
                                      ? "Connected to Google Analytics"
                                      : btn.name}
                                  </h5>
                                  {isConnected &&
                                    gaStatus?.connection?.property_name && (
                                      <p className="text-xs text-green-600 mt-1">
                                        {gaStatus.connection.property_name}
                                      </p>
                                    )}
                                </div>
                                {/* Status Icon */}
                                <div className="flex-shrink-0">
                                  {isConnected ? (
                                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                                      <svg
                                        className="w-3 h-3 text-white"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                      >
                                        <path
                                          fillRule="evenodd"
                                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                          clipRule="evenodd"
                                        />
                                      </svg>
                                    </div>
                                  ) : (
                                    <svg
                                      className="w-4 h-4 text-gray-400 group-hover:text-primary transition-colors"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 5l7 7-7 7"
                                      />
                                    </svg>
                                  )}
                                </div>
                              </button>
                            );
                          }

                          // Google Search Console button - standalone action button
                          if (btn.id === 2) {
                            const isConnected =
                              isGoogleSearchConsoleConnected();
                            return (
                              <button
                                onClick={() => {
                                  if (isConnected) {
                                    // If already connected, show property dialog
                                    setShowGSCPropertyDialog(true);
                                  } else if (TESTING_MODE) {
                                    // In testing mode, show modal directly
                                    setShowGSCPropertyDialog(true);
                                  } else if (projectId) {
                                    connectGSC();
                                  }
                                }}
                                disabled={gscConnecting}
                                key={index}
                                className={`group relative overflow-hidden transition-all duration-200 ease-in-out border rounded-lg p-4 flex items-center gap-3 text-left ${
                                  isConnected
                                    ? "border-green-200 bg-green-50 hover:bg-green-100"
                                    : "border-gray-200 bg-white hover:border-primary/50 hover:bg-primary/5"
                                } ${
                                  gscConnecting ? "opacity-70 cursor-wait" : ""
                                }`}
                              >
                                {/* Loading Indicator for Google Search Console */}
                                {gscConnecting && (
                                  <div className="absolute inset-0 bg-white/50 flex items-center justify-center">
                                    <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                                  </div>
                                )}

                                {/* Service Icon */}
                                <div className="flex-shrink-0">
                                  <LogosGoogleSearchConsole className="w-6 h-6" />
                                </div>

                                {/* Service Name */}
                                <div className="flex-1">
                                  <h5
                                    className={`text-sm font-medium transition-colors ${
                                      isConnected
                                        ? "text-green-700"
                                        : "text-[#344054] group-hover:text-primary"
                                    }`}
                                  >
                                    {gscConnecting
                                      ? "Connecting..."
                                      : isConnected
                                      ? "Connected to Google Search Console"
                                      : btn.name}
                                  </h5>
                                  {isConnected &&
                                    gscStatus?.connection?.property_url && (
                                      <p className="text-xs text-green-600 mt-1">
                                        {gscStatus.connection.property_url}
                                      </p>
                                    )}
                                </div>

                                {/* Status Icon */}
                                <div className="flex-shrink-0">
                                  {isConnected ? (
                                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                                      <svg
                                        className="w-3 h-3 text-white"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                      >
                                        <path
                                          fillRule="evenodd"
                                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                          clipRule="evenodd"
                                        />
                                      </svg>
                                    </div>
                                  ) : (
                                    <svg
                                      className="w-4 h-4 text-gray-400 group-hover:text-primary transition-colors"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 5l7 7-7 7"
                                      />
                                    </svg>
                                  )}
                                </div>
                              </button>
                            );
                          }

                          // Other service buttons - selectable (if any future services)
                          return (
                            <button
                              onClick={() => setBtnCheck(btn.id)}
                              key={index}
                              className={`group relative overflow-hidden transition-all duration-200 ease-in-out ${
                                btn.id === btnCheck
                                  ? "border-2 border-primary bg-primary/5"
                                  : "border border-gray-200 bg-white hover:border-primary/50"
                              } rounded-lg p-4 flex items-center gap-3 text-left`}
                            >
                              {/* Selection Indicator */}
                              {btn.id === btnCheck && (
                                <div className="absolute top-2 right-2 w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                                  <svg
                                    className="w-2.5 h-2.5 text-white"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                </div>
                              )}

                              {/* Service Icon */}
                              <div className="flex-shrink-0">
                                <Image
                                  alt={btn.name}
                                  src={btn.image}
                                  width={20}
                                  height={20}
                                />
                              </div>

                              {/* Service Name */}
                              <div className="flex-1">
                                <h5
                                  className={`text-sm font-medium transition-colors ${
                                    btn.id === btnCheck
                                      ? "text-primary"
                                      : "text-[#344054] group-hover:text-primary"
                                  }`}
                                >
                                  {btn.name}
                                </h5>
                              </div>
                            </button>
                          );
                        })}
                      </div>
                    </div>

                    {/* Info Cards */}
                    <div className="grid gap-4 md:grid-cols-2 mt-6">
                      <InfoCard
                        variant="info"
                        title="Setup Information"
                        description="The integration process may take several minutes to complete. You can skip this step and configure it later in your dashboard."
                      />
                      <InfoCard
                        variant="benefits"
                        title="Integration Benefits"
                        description={
                          <ul className="space-y-1">
                            <li>• Comprehensive SEO performance tracking</li>
                            <li>• Automated data synchronization</li>
                            <li>• Advanced reporting and insights</li>
                          </ul>
                        }
                      />
                    </div>
                  </AnimatedElement>
                </div>
              </AnimatedElement>
            </BoxCreateProject>
          </AnimatedElement>
        </div>

        {/* Action Buttons */}
        <AnimatedElement variant="card" delay={0.6}>
          <div className="flex flex-col sm:flex-row gap-3 justify-end bg-white p-6 rounded-xl border border-gray-100 shadow-sm">
            <Link href={urls.competitors}>
              <ButtenSubmit
                text="Back"
                color="primary__outline_hover"
                classPluss="sm:w-auto w-full order-2 sm:order-1"
              />
            </Link>
            <ButtenSubmit
              text="Complete Setup"
              textloading="Finalizing..."
              isLoading={isPending}
              onClick={() => mutate()}
              classPluss="sm:w-auto w-full order-1 sm:order-2"
            />
          </div>
        </AnimatedElement>
      </div>

      {/* Google Analytics Property Selection Dialog */}
      {(projectId || TESTING_MODE) && (
        <GoogleAnalyticsPropertyDialog
          isOpen={showPropertyDialog}
          onClose={() => setShowPropertyDialog(false)}
          projectId={projectId || "test-project-id"}
          onPropertySelected={handlePropertySelected}
          testingMode={TESTING_MODE}
        />
      )}

      {/* Google Search Console Property Selection Dialog */}
      {(projectId || TESTING_MODE) && (
        <GoogleSearchConsolePropertyDialog
          isOpen={showGSCPropertyDialog}
          onClose={() => setShowGSCPropertyDialog(false)}
          projectId={projectId || "test-project-id"}
          onPropertySelected={handleGSCPropertySelected}
          testingMode={TESTING_MODE}
        />
      )}
    </PageTransition>
  );
}
