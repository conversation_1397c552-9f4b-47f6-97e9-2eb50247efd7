"use client";
import React, { useEffect, useRef, useState } from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "../../../../../_components/date-range/DateRange";
import CardTab from "@/components/ui/card-tab/CardTab";
import NoData from "../../../../_components/NoData";
import Dropdown from "@/components/ui/Dropdown";
import usePagesBars from "./PagesBars.hooks";
import HorizontalBar from "@/components/ui/horizontal-bar/HorizontalBar";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* ================================= LODASH ================================= */
import isEqual from "lodash.isequal";

/* ================================== TYPES ================================= */
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";

/* ========================================================================== */
const PagesBars = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [activeTab, setActiveTab] = useState("");
  const { themeColor } = useAppThemeColor();
  const filters = ["All Pages", "Landing Pages", "High Traffic Page"];
  const [activeFilter, setActiveDomesticFilter] = useState(filters[0]);

  const { data, isError, error, isLoading } = usePagesBars({
    tab: activeTab,
    filter: activeFilter,
  });

  const prevCardTabsRef = useRef<CardTabType[] | null>(data?.cardTabs ?? null);
  const [cardsDataChanged, setCardsDataChanged] = useState(false);

  useEffect(() => {
    if (data?.cardTabs && !isEqual(prevCardTabsRef.current, data.cardTabs)) {
      prevCardTabsRef.current = data.cardTabs;
      setCardsDataChanged(true);
    } else {
      setCardsDataChanged(false);
    }
  }, [data?.cardTabs]);

  useEffect(() => {
    if (data && !activeTab) setActiveTab(data?.cardTabs[0].title);
  }, [data, activeTab]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  if (!isError) {
    return (
      <Card className="space-y-4">
        <div className="space-y-2">
          <div className="flex w-full justify-between items-center">
            <Title>Pages</Title>
            <Dropdown>
              <Dropdown.Button>{activeFilter}</Dropdown.Button>
              <Dropdown.Options>
                {filters.map((filter, index) => (
                  <Dropdown.Option
                    key={index}
                    onClick={() => setActiveDomesticFilter(filter)}
                  >
                    {filter}
                  </Dropdown.Option>
                ))}
              </Dropdown.Options>
            </Dropdown>
          </div>
          <DateRange />
        </div>
        <div className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap">
          {cardsDataChanged ? (
            <motion.div
              className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
              key={"loading"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
              {Array.from({ length: 7 }).map((_, i) => (
                <CardTab key={i} isLoading />
              ))}
            </motion.div>
          ) : (
            prevCardTabsRef.current && (
              <motion.div
                className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
                key={"data"}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                {prevCardTabsRef.current.map(
                  (
                    {
                      title,
                      changeValue,
                      value,
                    }: { title: string; changeValue: string; value: string },
                    index: number
                  ) => (
                    <CardTab
                      key={index}
                      title={title}
                      value={value}
                      changeValue={changeValue}
                      className={`border-2`}
                      style={
                        activeTab === title
                          ? { borderColor: themeColor }
                          : { borderColor: "transparent" }
                      }
                      onSelect={() => setActiveTab(title)}
                    />
                  )
                )}
              </motion.div>
            )
          )}
        </div>
        <div className="m-8 mb-5 px-1 py-6 border-l border-b border-secondary/30 space-y-2 min-h-[435px]">
          {isLoading
            ? Array.from({ length: 7 }).map((_, i) => (
                <HorizontalBar isLoading key={i} />
              ))
            : data &&
              data.barsData.bars.map(({ barData, label }, index) => (
                <HorizontalBar
                  percentageLabel={index < 2 ? "of all users" : ""}
                  key={index}
                  label={label}
                  bars={barData}
                  totalValue={data.barsData.maxValue}
                />
              ))}
        </div>
      </Card>
    );
  }

  if (isError) {
    console.error("fetching chart and bars failed: ", error);
    return <NoData title="About Users" />;
  }
};

export default PagesBars;
